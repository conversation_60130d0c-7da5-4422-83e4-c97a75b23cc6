"use client";

import { useState, useEffect, useCallback } from "react";

// Declaración de tipos para Hotmart
declare global {
  interface Window {
    checkoutElements?: {
      init: (type: string, options: any) => {
        mount: (selector: string) => void;
      };
    };
  }
}
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckIcon, Zap, Infinity, AlertTriangle, Sparkles } from "lucide-react";
import Script from "next/script";
import Link from "next/link";
import { PayPalScriptProvider, PayPalButtons } from "@paypal/react-paypal-js";
import CheckoutHeader from "@/components/layout/CheckoutHeader";
import PayPalLogo from "@/components/icons/PayPalLogo";
import PayPalIcon from "@/components/icons/PayPalIcon";
import WorldIcon from "@/components/icons/WorldIcon";

const CheckoutPage = () => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [isHotmartLoaded, setIsHotmartLoaded] = useState(false);
  const [isHotmartLoading, setIsHotmartLoading] = useState(false);
  const [hotmartLoadAttempts, setHotmartLoadAttempts] = useState(0);

  // Función para cargar el script de Hotmart
  const loadHotmartScript = useCallback(() => {
    if (isHotmartLoading) return; // Evitar cargas múltiples simultáneas

    setIsHotmartLoading(true);

    // Eliminar cualquier script anterior si existe
    const existingScript = document.getElementById('hotmart-script');
    if (existingScript) {
      document.body.removeChild(existingScript);
    }

    // Limpiar el contenedor de checkout
    const checkoutContainer = document.getElementById('inline_checkout');
    if (checkoutContainer) {
      checkoutContainer.innerHTML = '<div class="animate-pulse text-white/70">Cargando formulario de pago...</div>';
    }

    // Crear y cargar el nuevo script
    const script = document.createElement("script");
    script.id = 'hotmart-script';
    script.src = "https://checkout.hotmart.com/lib/hotmart-checkout-elements.js";
    script.async = true;

    script.onload = () => {
      if (window.checkoutElements) {
        try {
          const elements = window.checkoutElements.init("inlineCheckout", {
            offer: "mz63zpyh",
          });

          elements.mount("#inline_checkout");
          setIsHotmartLoaded(true);
          setIsHotmartLoading(false);
          setHotmartLoadAttempts(0); // Reiniciar los intentos cuando se carga con éxito

          console.log("Hotmart cargado exitosamente");
        } catch (error) {
          console.error("Error al inicializar Hotmart:", error);
          setIsHotmartLoading(false);
          retryHotmartLoad();
        }
      } else {
        console.error("checkoutElements no está disponible");
        setIsHotmartLoading(false);
        retryHotmartLoad();
      }
    };

    script.onerror = () => {
      console.error("Error al cargar el script de Hotmart");
      setIsHotmartLoading(false);
      retryHotmartLoad();
    };

    document.body.appendChild(script);

    return script;
  }, [isHotmartLoading]);

  // Función para reintentar la carga de Hotmart
  const retryHotmartLoad = useCallback(() => {
    const maxAttempts = 3;
    if (hotmartLoadAttempts < maxAttempts) {
      setHotmartLoadAttempts(prev => prev + 1);
      setTimeout(() => {
        console.log(`Reintentando cargar Hotmart (intento ${hotmartLoadAttempts + 1}/${maxAttempts})`);
        loadHotmartScript();
      }, 1000); // Esperar 1 segundo antes de reintentar
    } else {
      console.error(`No se pudo cargar Hotmart después de ${maxAttempts} intentos`);
    }
  }, [hotmartLoadAttempts, loadHotmartScript]);

  // Efecto para cargar Hotmart cuando se selecciona como método de pago
  useEffect(() => {
    let script: HTMLScriptElement | null = null;

    if (selectedPaymentMethod === "hotmart") {
      script = loadHotmartScript();
    }

    return () => {
      if (script && script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [selectedPaymentMethod, loadHotmartScript]);

  const paypalOptions = {
    "client-id": "ARoSv53ctY4XSQw6eGen9Mr44GkmEniwbNfhmQqIeD1YzgTjo2wYdazS7rMwgjrMhDO6eEx8dUq_L_yz",
    currency: "USD",
    intent: "capture",
    components: "buttons",
    locale: "es_ES",
    "disable-funding": "credit,card,sofort",
  };

  return (
    <div className="min-h-screen" style={{ background: "linear-gradient(to bottom, #0f0f1a, #1a1a2e)" }}>
      <CheckoutHeader />
      <div className="container-custom py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Columna derecha - Resumen de compra (aparece primero en móvil) */}
          <div className="w-full lg:w-1/3 order-1 lg:order-2 mb-8 lg:mb-0">
            <Card className="border border-[#2a2a4a] bg-[#1a1a2e] p-6 rounded-xl">
              <div className="flex justify-between items-center mb-2">
                <div>
                  <h2 className="text-2xl font-bold text-white">Flasti</h2>
                  <p className="text-sm text-white/70">Acceso completo a la plataforma</p>
                </div>
                <div className="bg-[#ec4899] text-white text-xs font-bold px-2 py-1 rounded-full">
                  Exclusivo
                </div>
              </div>
              
              <div className="bg-[#0f0f1a] rounded-xl border border-[#2a2a4a] p-4 mt-4 mb-4">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-4xl font-bold text-white">$10</span>
                  <div className="flex flex-col">
                    <span className="text-sm text-white/70">USD</span>
                    <div className="flex items-center gap-1">
                      <span className="text-sm line-through text-red-500">$100</span>
                      <span className="text-xs font-bold bg-green-500 text-black px-1.5 py-0.5 rounded">90% OFF</span>
                    </div>
                  </div>
                </div>
                <p className="text-xs text-white/70">Pago único - Sin suscripciones ni cargos recurrentes</p>
              </div>

              <div className="flex flex-col gap-3 mb-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-[#9333ea]/20 to-[#ec4899]/20 flex items-center justify-center flex-shrink-0 border border-[#3a3a5a]">
                    <Zap className="text-[#ec4899]" size={16} />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-white">Acceso inmediato</h4>
                    <p className="text-xs text-white/70">Comienza a generar ingresos hoy mismo</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-[#9333ea]/20 to-[#ec4899]/20 flex items-center justify-center flex-shrink-0 border border-[#3a3a5a]">
                    <Infinity className="text-[#ec4899]" size={16} />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-white">Acceso de por vida</h4>
                    <p className="text-xs text-white/70">Sin límites de tiempo ni renovaciones</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3 mb-6">
                <h3 className="font-medium text-white">Lo que obtienes:</h3>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2 text-sm text-white">
                    <CheckIcon className="text-[#22c55e] flex-shrink-0 mt-1" size={16} />
                    <span>Acceso completo a la plataforma de microtrabajos asistidos por IA</span>
                  </li>
                  <li className="flex items-start gap-2 text-sm text-white">
                    <CheckIcon className="text-[#22c55e] flex-shrink-0 mt-1" size={16} />
                    <span>Herramientas de automatización para maximizar tus ganancias</span>
                  </li>
                  <li className="flex items-start gap-2 text-sm text-white">
                    <CheckIcon className="text-[#22c55e] flex-shrink-0 mt-1" size={16} />
                    <span>Tutoriales y guías paso a paso para comenzar desde cero</span>
                  </li>
                  <li className="flex items-start gap-2 text-sm text-white">
                    <CheckIcon className="text-[#22c55e] flex-shrink-0 mt-1" size={16} />
                    <span>Acceso a la comunidad exclusiva de usuarios de Flasti</span>
                  </li>
                </ul>
              </div>
            </Card>
          </div>
          
          {/* Columna izquierda - Métodos de pago */}
          <div className="w-full lg:w-2/3 order-2 lg:order-1">
            <div className="mb-8">
              <h1 className="text-2xl font-bold mb-2 text-white">Información de pago</h1>
              <p className="text-white/70 text-sm">Todas las transacciones son seguras y encriptadas</p>
            </div>

            {/* Opciones de pago */}
            <div className="space-y-4">
              <div className="flex items-center justify-center text-xs text-white/70 mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                </svg>
                Pago 100% seguro, protegemos tus datos.
              </div>
              
              {/* Moneda local - Hotmart */}
              <Card className={`border border-[#2a2a4a] bg-[#1a1a2e] overflow-hidden rounded-xl ${selectedPaymentMethod === "hotmart" ? "border-[#ec4899]" : ""}`}>
                <div
                  className="p-4 cursor-pointer flex items-center justify-between"
                  onClick={() => {
                    // Si ya está seleccionado, deseleccionarlo
                    if (selectedPaymentMethod === "hotmart") {
                      setSelectedPaymentMethod(null);
                      // Cambiar el método de pago
                    } else {
                      setSelectedPaymentMethod("hotmart");
                    }
                  }}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#9333ea]/20 to-[#ec4899]/20 flex items-center justify-center border border-[#3a3a5a]">
                      <WorldIcon className="text-white h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-medium text-white">Moneda local</h3>
                      <div className="flex flex-wrap items-center gap-1 mt-3 w-full lg:flex-nowrap sm:flex-wrap max-w-[180px] sm:max-w-[350px] lg:max-w-none lg:overflow-x-auto lg:pb-1">
                        {[
                          "🇦🇷", // Argentina
                          "🇨🇴", // Colombia
                          "🇻🇪", // Venezuela
                          "🇵🇪", // Perú
                          "🇲🇽", // México
                          "🇵🇦", // Panamá
                          "🇬🇹", // Guatemala
                          "🇸🇻", // El Salvador
                          "🇩🇴", // República Dominicana
                          "🇵🇷", // Puerto Rico
                          "🇪🇨", // Ecuador
                          "🇵🇾", // Paraguay
                          "🇪🇸", // España
                          "🇨🇷", // Costa Rica
                          "🇨🇱", // Chile
                          "🇺🇾", // Uruguay
                          "🇧🇴", // Bolivia
                          "🇭🇳"  // Honduras
                        ].map((flag, index) => (
                          <span key={index} className="w-4 h-4 lg:w-5 lg:h-5 rounded-full overflow-hidden flex items-center justify-center bg-white">
                            <span className="text-[8px] lg:text-[10px] font-bold">{flag}</span>
                          </span>
                        ))}
                      </div>
                      <p className="text-sm text-white/70 mt-3">Realiza tu pago rápido y seguro en tu moneda</p>
                    </div>
                  </div>
                  <div className="w-6 h-6 rounded-full border border-[#3a3a5a] flex items-center justify-center">
                    {selectedPaymentMethod === "hotmart" && <CheckIcon className="h-4 w-4 text-[#ec4899]" />}
                  </div>
                </div>

                {selectedPaymentMethod === "hotmart" && (
                  <div className="p-6 border-t border-[#2a2a4a]" style={{ background: "linear-gradient(to bottom, #0f0f1a, #1a1a2e)" }}>
                    <div id="inline_checkout" className="min-h-[300px] flex items-center justify-center rounded-lg overflow-hidden">
                      <div className="animate-pulse text-white/70">Cargando formulario de pago...</div>
                    </div>
                  </div>
                )}
              </Card>

              {/* PayPal */}
              <Card className={`border border-[#2a2a4a] bg-[#1a1a2e] overflow-hidden rounded-xl ${selectedPaymentMethod === "paypal" ? "border-[#ec4899]" : ""}`}>
                <div
                  className="p-4 cursor-pointer flex items-center justify-between"
                  onClick={() => {
                    // Si ya está seleccionado, deseleccionarlo
                    if (selectedPaymentMethod === "paypal") {
                      setSelectedPaymentMethod(null);
                    } else {
                      setSelectedPaymentMethod("paypal");
                    }
                  }}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#9333ea]/20 to-[#ec4899]/20 flex items-center justify-center border border-[#3a3a5a]">
                      <div className="flex items-center justify-center w-full h-full pl-0.5">
                        <PayPalIcon className="text-white h-5 w-5 flex-shrink-0" />
                      </div>
                    </div>
                    <div>
                      <h3 className="font-medium text-white">PayPal</h3>
                      <p className="text-sm text-white/70">Paga de forma segura con tu cuenta de PayPal</p>
                    </div>
                  </div>
                  <div className="w-6 h-6 rounded-full border border-[#3a3a5a] flex items-center justify-center">
                    {selectedPaymentMethod === "paypal" && <CheckIcon className="h-4 w-4 text-[#ec4899]" />}
                  </div>
                </div>

                {selectedPaymentMethod === "paypal" && (
                  <div className="p-6 border-t border-[#2a2a4a]">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="font-medium mb-1 text-white">Pago con PayPal</h3>
                        <p className="text-sm text-white/70">Activa tu acceso al instante con PayPal de forma rápida y segura.</p>
                      </div>
                      <div className="text-white">
                        <PayPalLogo className="h-6 w-auto" />
                      </div>
                    </div>

                    <div className="flex justify-between items-center mb-6 p-3 bg-[#0f0f1a] rounded-lg border border-[#2a2a4a]">
                      <span className="text-sm text-white">Total</span>
                      <div className="flex flex-col items-end">
                        <div className="flex items-center gap-3">
                          <span className="text-sm line-through text-red-500">$100 USD</span>
                          <span className="text-xs font-bold bg-green-500 text-black px-1.5 py-0.5 rounded">90% OFF</span>
                        </div>
                        <span className="font-bold text-white mt-1">$ 10 USD</span>
                      </div>
                    </div>

                    <div className="mb-6">
                      {/* Mostramos directamente los botones de PayPal */}
                      <PayPalScriptProvider options={paypalOptions}>
                        <PayPalButtons
                          style={{
                            color: "blue",
                            shape: "pill",
                            label: "pay",
                            height: 45,
                          }}
                          createOrder={(data, actions) => {
                            return actions.order.create({
                              purchase_units: [
                                {
                                  amount: {
                                    value: "10.00",
                                  },
                                  description: "Acceso a Flasti",
                                },
                              ],
                            });
                          }}
                          onApprove={(data, actions) => {
                            return actions.order.capture().then((details) => {
                              console.log("Pago completado. ID de transacción: " + details.id);
                              // Redirigir al usuario a la página de registro
                              window.location.href = "/register";
                            });
                          }}
                        />
                      </PayPalScriptProvider>
                    </div>

                    <div className="flex items-center justify-center text-xs text-white/70 mt-4">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                      </svg>
                      Pago 100% seguro, protegemos tus datos.
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Fin de la columna izquierda */}
              <div className="flex justify-between items-center mb-2">
                <div>
                  <h2 className="text-2xl font-bold text-white">Flasti</h2>
                  <p className="text-sm text-white/70">Acceso completo a la plataforma</p>
                </div>
                <div className="bg-[#ec4899] text-white text-xs font-bold px-2 py-1 rounded-full">
                  Exclusivo
                </div>
              </div>

              <div className="bg-[#0f0f1a] rounded-xl border border-[#2a2a4a] p-4 mt-4 mb-4">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-4xl font-bold text-white">$10</span>
                  <div className="flex flex-col">
                    <span className="text-sm text-white/70">USD</span>
                    <div className="flex items-center gap-1">
                      <span className="text-sm line-through text-red-500">$100</span>
                      <span className="text-xs font-bold bg-green-500 text-black px-1.5 py-0.5 rounded">90% OFF</span>
                    </div>
                  </div>
                </div>
                <p className="text-xs text-white/70">Pago único - Sin suscripciones ni cargos recurrentes</p>
              </div>



              <div className="space-y-3 mb-6">
                <h3 className="font-medium text-white">Lo que obtienes:</h3>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2 text-sm text-white">
                    <CheckIcon className="text-[#22c55e] flex-shrink-0 mt-1" size={16} />
                    <span>Acceso completo a la plataforma de microtrabajos asistidos por IA</span>
                  </li>
                  <li className="flex items-start gap-2 text-sm text-white">
                    <CheckIcon className="text-[#22c55e] flex-shrink-0 mt-1" size={16} />
                    <span>Herramientas de automatización para maximizar tus ganancias</span>
                  </li>
                  <li className="flex items-start gap-2 text-sm text-white">
                    <CheckIcon className="text-[#22c55e] flex-shrink-0 mt-1" size={16} />
                    <span>Tutoriales y guías paso a paso para comenzar desde cero</span>
                  </li>
                  <li className="flex items-start gap-2 text-sm text-white">
                    <CheckIcon className="text-[#22c55e] flex-shrink-0 mt-1" size={16} />
                    <span>Acceso a la comunidad exclusiva de usuarios de Flasti</span>
                  </li>
                </ul>
              </div>


            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
